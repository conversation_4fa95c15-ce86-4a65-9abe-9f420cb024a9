<template>
  <section id="why-wrlds" class="relative py-16 md:py-24 bg-white overflow-hidden">
    <div class="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12 md:mb-16">
        <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3">
          Why WRLDS?
        </h2>
        <p class="text-gray-600 text-lg max-w-3xl mx-auto">
          In an industry where complexity leads to failure, we bring simplicity and expertise to ensure your success
        </p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
        <div 
          v-for="stat in stats" 
          :key="stat.title"
          class="bg-gray-100 p-6 rounded-xl border border-gray-200 text-center hover:bg-gray-200 transition-all"
        >
          <div class="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center mx-auto mb-4">
            <component :is="stat.icon" class="w-8 h-8 text-gray-700" />
          </div>
          <h3 class="text-gray-900 text-2xl lg:text-3xl font-bold mb-3">
            {{ stat.value }}
          </h3>
          <p class="text-gray-600 text-sm">{{ stat.description }}</p>
        </div>
      </div>
      
      <div class="mb-12">
        <div class="text-center mb-8">
          <h3 class="text-2xl md:text-3xl font-bold text-gray-900 mb-3">
            What WRLDS Does for You
          </h3>
          <p class="text-gray-600 max-w-2xl mx-auto">
            We simplify the complex world of smart textiles, providing end-to-end solutions that deliver real results.
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div 
            v-for="benefit in benefits" 
            :key="benefit.title"
            class="text-center p-6 bg-white rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-300"
          >
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <component :is="benefit.icon" class="w-6 h-6 text-blue-600" />
            </div>
            <h4 class="font-semibold text-gray-900 mb-2">{{ benefit.title }}</h4>
            <p class="text-gray-600 text-sm">{{ benefit.description }}</p>
          </div>
        </div>
      </div>
      
      <div class="text-center">
        <h3 class="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
          Ready to Transform Your Industry?
        </h3>
        <p class="text-gray-600 mb-8 max-w-2xl mx-auto">
          Join the companies already benefiting from our smart textile solutions. Let's discuss how we can help you innovate.
        </p>
        <button 
          @click="scrollToContact"
          class="inline-flex items-center px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Get Started Today
          <ArrowRight class="ml-2 w-5 h-5" />
        </button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { BarChart, Clock, Target, Shield, Rocket, Zap, Award, ArrowRight } from 'lucide-vue-next'

const stats = [
  {
    icon: BarChart,
    value: "$3.8B USD",
    title: "Market Size",
    description: "Global smart textiles market size by 2025"
  },
  {
    icon: Clock,
    value: "50%",
    title: "Faster Development",
    description: "Reduced time-to-market with our platform"
  },
  {
    icon: Target,
    value: "99.9%",
    title: "Accuracy",
    description: "Sensor data accuracy in real-world conditions"
  }
]

const benefits = [
  {
    icon: Rocket,
    title: "Rapid Prototyping",
    description: "From concept to working prototype in weeks, not months"
  },
  {
    icon: Shield,
    title: "Proven Reliability",
    description: "Battle-tested solutions across multiple industries"
  },
  {
    icon: Zap,
    title: "Scalable Platform",
    description: "Technology that grows with your business needs"
  },
  {
    icon: Award,
    title: "Expert Support",
    description: "Dedicated team of textile and technology specialists"
  }
]

const scrollToContact = () => {
  const contactSection = document.getElementById('contact')
  if (contactSection) {
    contactSection.scrollIntoView({
      behavior: 'smooth'
    })
  }
}
</script>
