<template>
  <div class="min-h-screen bg-white w-full max-w-[100vw] overflow-x-hidden">
    <Navbar />
    <slot />
    <ContactInfo v-if="showContact" />
    <Footer />
    <FloatingContactButton v-if="showContact" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import Navbar from './Navbar.vue'
import Footer from './Footer.vue'
import ContactInfo from './ContactInfo.vue'
import FloatingContactButton from './FloatingContactButton.vue'

interface Props {
  showContact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showContact: true
})

const route = useRoute()

// Effect to scroll to top when route changes
watch(() => route.path, () => {
  window.scrollTo(0, 0)
})

onMounted(() => {
  window.scrollTo(0, 0)
})
</script>
