<template>
  <div class="relative mt-16 md:mt-0 w-full">
    <div class="banner-container bg-black relative overflow-hidden h-[60vh] sm:h-[70vh] md:h-[750px] w-full">
      <div class="absolute inset-0 bg-black w-full">
        <img 
          src="/lovable-uploads/4bfa0d71-3ed2-4693-90b6-35142468907f.png" 
          alt="WRLDS Technologies Connected People" 
          :class="cn('w-full h-full object-cover opacity-70 grayscale', isMobile ? 'object-right' : 'object-center')"
        />
        <div class="absolute inset-0 bg-gradient-to-b from-black/70 via-black/60 to-white"></div>
      </div>
      
      <div class="banner-overlay bg-transparent pt-16 sm:pt-20 md:pt-24 w-full">
        <div class="w-full mx-auto px-4 sm:px-6 lg:px-8 flex flex-col items-center justify-center h-full">
          <div class="w-full max-w-4xl text-center">
            <h1 class="banner-title text-white text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold leading-tight">
              The Future of Smart Textile Technology is here.
            </h1>
            <p class="banner-subtitle text-gray-300 mt-4 sm:mt-6 text-lg sm:text-xl md:text-2xl max-w-3xl mx-auto">
              We integrate AI-powered textile sensors into clothing, footwear, and wearables.
            </p>
            <div class="flex flex-col sm:flex-row gap-3 sm:gap-4 mt-6 sm:mt-8 justify-center items-center">
              <button 
                class="w-full sm:w-auto min-h-[44px] px-6 sm:px-8 py-3 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-all shadow-lg hover:shadow-xl hover:shadow-gray-300/20 flex items-center justify-center group text-sm sm:text-base font-medium"
                @click="scrollToProjects"
              >
                Explore Projects
                <ArrowRight class="ml-2 w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-1 transition-transform" />
              </button>
              
              <button 
                class="w-full sm:w-auto min-h-[44px] px-6 sm:px-8 py-3 bg-gray-700 text-white rounded-md hover:bg-gray-800 transition-all shadow-lg hover:shadow-xl hover:shadow-gray-300/20 flex items-center justify-center group text-sm sm:text-base font-medium"
                @click="scrollToContact"
              >
                Contact Us
                <MessageSquare class="ml-2 w-4 h-4 sm:w-5 sm:h-5 group-hover:scale-110 transition-transform" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="relative z-10 w-full px-4 sm:px-6 lg:px-8 mx-auto">
      <div class="mt-6 md:mt-8 grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-4">
        <div class="bg-white p-4 md:p-5 rounded-xl shadow-sm border border-gray-100 transform transition-all duration-300 hover:-translate-y-1 hover:shadow-md">
          <div class="w-10 h-10 md:w-12 md:h-12 bg-gray-100 flex items-center justify-center rounded-lg text-gray-500 mb-2 md:mb-3">
            <Cpu class="w-5 h-5 md:w-6 md:h-6" />
          </div>
          <h3 class="text-base md:text-lg font-semibold mb-1 md:mb-2 text-gray-800">Smart Textiles</h3>
          <p class="text-gray-600 text-xs md:text-sm">Intelligent fabric sensors that seamlessly integrate into clothing and footwear.</p>
        </div>
        
        <div class="bg-white p-4 md:p-5 rounded-xl shadow-sm border border-gray-100 transform transition-all duration-300 hover:-translate-y-1 hover:shadow-md">
          <div class="w-10 h-10 md:w-12 md:h-12 bg-gray-100 flex items-center justify-center rounded-lg text-gray-500 mb-2 md:mb-3">
            <Code class="w-5 h-5 md:w-6 md:h-6" />
          </div>
          <h3 class="text-base md:text-lg font-semibold mb-1 md:mb-2 text-gray-800">Adaptive AI</h3>
          <p class="text-gray-600 text-xs md:text-sm">Industry-specific algorithms that transform textile sensor data into meaningful insights.</p>
        </div>
        
        <div class="bg-white p-4 md:p-5 rounded-xl shadow-sm border border-gray-100 transform transition-all duration-300 hover:-translate-y-1 hover:shadow-md">
          <div class="w-10 h-10 md:w-12 md:h-12 bg-gray-100 flex items-center justify-center rounded-lg text-gray-500 mb-2 md:mb-3">
            <Layers class="w-5 h-5 md:w-6 md:h-6" />
          </div>
          <h3 class="text-base md:text-lg font-semibold mb-1 md:mb-2 text-gray-800">Cross-Industry</h3>
          <p class="text-gray-600 text-xs md:text-sm">Solutions for sports, military, healthcare, industrial, and professional environments.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowRight, Code, Cpu, Layers, MessageSquare } from 'lucide-vue-next'
import { cn } from '@/lib/utils'
import { useWindowSize } from '@vueuse/core'

const { width } = useWindowSize()
const isMobile = computed(() => width.value < 768)

const scrollToContact = () => {
  const contactSection = document.getElementById('contact')
  if (contactSection) {
    contactSection.scrollIntoView({
      behavior: 'smooth'
    })
  }
}

const scrollToProjects = () => {
  const projectsSection = document.getElementById('projects')
  if (projectsSection) {
    projectsSection.scrollIntoView({
      behavior: 'smooth'
    })
  }
}
</script>

<style scoped>
.banner-title {
  font-size: clamp(2rem, 8vw, 4.5rem);
  line-height: 1.1;
}

.banner-subtitle {
  font-size: clamp(1rem, 3vw, 1.5rem);
  line-height: 1.4;
}

.banner-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  text-align: center;
  z-index: 10;
}
</style>
