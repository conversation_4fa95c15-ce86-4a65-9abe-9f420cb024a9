<template>
  <section id="contact" class="bg-gray-50 py-16">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Get in Touch
        </h2>
        <p class="text-gray-600 max-w-2xl mx-auto">
          Ready to transform your products with smart textile technology? Let's discuss your project.
        </p>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <div>
          <h3 class="text-xl font-semibold text-gray-900 mb-6">Contact Information</h3>
          <div class="space-y-4">
            <div class="flex items-center">
              <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center mr-4">
                <Mail class="w-6 h-6 text-gray-600" />
              </div>
              <div>
                <p class="font-medium text-gray-900">Email</p>
                <p class="text-gray-600"><EMAIL></p>
              </div>
            </div>
            
            <div class="flex items-center">
              <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center mr-4">
                <Phone class="w-6 h-6 text-gray-600" />
              </div>
              <div>
                <p class="font-medium text-gray-900">Phone</p>
                <p class="text-gray-600">+****************</p>
              </div>
            </div>
            
            <div class="flex items-center">
              <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center mr-4">
                <MapPin class="w-6 h-6 text-gray-600" />
              </div>
              <div>
                <p class="font-medium text-gray-900">Address</p>
                <p class="text-gray-600">123 Innovation Drive<br>Tech City, TC 12345</p>
              </div>
            </div>
          </div>
        </div>
        
        <div>
          <form @submit.prevent="handleSubmit" class="space-y-6">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Name</label>
              <input
                type="text"
                id="name"
                v-model="form.name"
                required
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
              <input
                type="email"
                id="email"
                v-model="form.email"
                required
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
              <textarea
                id="message"
                v-model="form.message"
                rows="4"
                required
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              ></textarea>
            </div>
            
            <Button type="submit" class="w-full">
              Send Message
            </Button>
          </form>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { Mail, Phone, MapPin } from 'lucide-vue-next'
import Button from '../ui/Button.vue'

const form = reactive({
  name: '',
  email: '',
  message: ''
})

const handleSubmit = () => {
  // Handle form submission
  console.log('Form submitted:', form)
  // Reset form
  form.name = ''
  form.email = ''
  form.message = ''
}
</script>
