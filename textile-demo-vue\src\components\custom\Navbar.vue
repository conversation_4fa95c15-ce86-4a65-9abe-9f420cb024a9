<template>
  <nav 
    :class="cn(
      'fixed top-0 left-0 right-0 z-50 transition-all duration-300 w-full',
      isScrolled ? 'bg-white shadow-sm' : 'bg-black'
    )"
  >
    <div class="w-full px-4 sm:px-6 lg:px-8 mx-auto">
      <div class="flex items-center justify-between h-16">
        <div class="flex-shrink-0">
          <RouterLink to="/" class="flex items-center">
            <img 
              src="/lovable-uploads/7d120ee6-3614-4b75-9c35-716d54490d67.png" 
              alt="WRLDS Technologies Logo" 
              :class="cn('h-8 w-auto', isScrolled ? '' : 'brightness-0 invert')" 
            />
          </RouterLink>
        </div>
        
        <!-- Desktop Navigation -->
        <div class="hidden md:block">
          <div :class="cn('flex items-center space-x-4', isScrolled ? '' : 'text-white')">
            <RouterLink 
              to="/" 
              :class="cn(
                'px-3 py-2 rounded-md text-sm font-medium transition-colors',
                isScrolled ? 'text-gray-700 hover:text-gray-900' : 'text-gray-100 hover:text-white hover:bg-gray-800'
              )"
            >
              Home
            </RouterLink>
            
            <RouterLink 
              to="/about" 
              :class="cn(
                'px-3 py-2 rounded-md text-sm font-medium transition-colors',
                isScrolled ? 'text-gray-700 hover:text-gray-900' : 'text-gray-100 hover:text-white hover:bg-gray-800'
              )"
            >
              About Us
            </RouterLink>
            
            <!-- Customer Cases Dropdown -->
            <div class="relative" @mouseenter="showCustomerCases = true" @mouseleave="showCustomerCases = false">
              <button 
                :class="cn(
                  'flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors',
                  isScrolled ? 'text-gray-700 hover:text-gray-900' : 'text-gray-100 hover:text-white hover:bg-gray-800'
                )"
              >
                Customer Cases
                <ChevronDown class="ml-1 h-4 w-4" />
              </button>
              
              <div 
                v-show="showCustomerCases"
                class="absolute top-full left-0 mt-1 w-96 bg-white rounded-md shadow-lg border z-50"
              >
                <div class="p-4 space-y-3">
                  <RouterLink 
                    to="/projects/firecat" 
                    class="block p-3 space-y-1 rounded-md hover:bg-gray-100"
                    @click="showCustomerCases = false"
                  >
                    <div class="font-medium text-gray-900">FireCat 6th SENSE</div>
                    <p class="text-sm text-gray-500">Smart textiles for firefighter safety</p>
                  </RouterLink>
                  
                  <RouterLink 
                    to="/projects/sport-retail" 
                    class="block p-3 space-y-1 rounded-md hover:bg-gray-100"
                    @click="showCustomerCases = false"
                  >
                    <div class="font-medium text-gray-900">Sports Performance</div>
                    <p class="text-sm text-gray-500">Advanced tracking for athletes</p>
                  </RouterLink>
                  
                  <RouterLink 
                    to="/projects/workwear" 
                    class="block p-3 space-y-1 rounded-md hover:bg-gray-100"
                    @click="showCustomerCases = false"
                  >
                    <div class="font-medium text-gray-900">Workwear Climate Control</div>
                    <p class="text-sm text-gray-500">Temperature regulation for extreme environments</p>
                  </RouterLink>
                  
                  <RouterLink 
                    to="/projects/hockey" 
                    class="block p-3 space-y-1 rounded-md hover:bg-gray-100"
                    @click="showCustomerCases = false"
                  >
                    <div class="font-medium text-gray-900">Ice Hockey Elite Tracker</div>
                    <p class="text-sm text-gray-500">Performance tracking for ice hockey</p>
                  </RouterLink>
                  
                  <RouterLink 
                    to="/projects/pet-tracker" 
                    class="block p-3 space-y-1 rounded-md hover:bg-gray-100"
                    @click="showCustomerCases = false"
                  >
                    <div class="font-medium text-gray-900">Pet Activity Counter</div>
                    <p class="text-sm text-gray-500">Smart collars for pet activity monitoring</p>
                  </RouterLink>
                </div>
              </div>
            </div>
            
            <!-- Learn More Dropdown -->
            <div class="relative" @mouseenter="showLearnMore = true" @mouseleave="showLearnMore = false">
              <button 
                :class="cn(
                  'flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors',
                  isScrolled ? 'text-gray-700 hover:text-gray-900' : 'text-gray-100 hover:text-white hover:bg-gray-800'
                )"
              >
                Learn More
                <ChevronDown class="ml-1 h-4 w-4" />
              </button>
              
              <div 
                v-show="showLearnMore"
                class="absolute top-full left-0 mt-1 w-96 bg-white rounded-md shadow-lg border z-50"
              >
                <div class="p-4 space-y-3">
                  <RouterLink 
                    to="/tech-details" 
                    class="block p-3 space-y-1 rounded-md hover:bg-gray-100"
                    @click="showLearnMore = false"
                  >
                    <div class="font-medium text-gray-900">Technology Details</div>
                    <p class="text-sm text-gray-500">How our smart textile platform works</p>
                  </RouterLink>
                  
                  <RouterLink 
                    to="/development-process" 
                    class="block p-3 space-y-1 rounded-md hover:bg-gray-100"
                    @click="showLearnMore = false"
                  >
                    <div class="font-medium text-gray-900">Development Process</div>
                    <p class="text-sm text-gray-500">Our approach to creating custom solutions</p>
                  </RouterLink>
                </div>
              </div>
            </div>
            
            <RouterLink 
              to="/blog" 
              :class="cn(
                'px-3 py-2 rounded-md text-sm font-medium transition-colors',
                isScrolled ? 'text-gray-700 hover:text-gray-900' : 'text-gray-100 hover:text-white hover:bg-gray-800'
              )"
            >
              News
            </RouterLink>
            
            <RouterLink 
              to="/careers" 
              :class="cn(
                'px-3 py-2 rounded-md text-sm font-medium transition-colors',
                isScrolled ? 'text-gray-700 hover:text-gray-900' : 'text-gray-100 hover:text-white hover:bg-gray-800'
              )"
            >
              Careers
            </RouterLink>
            
            <button 
              @click="scrollToSection('contact')" 
              :class="cn(
                'px-4 py-2 rounded-md transition-colors',
                isScrolled ? 'bg-gray-200 text-gray-700 hover:bg-gray-300' : 'bg-gray-700 text-white hover:bg-gray-600'
              )"
            >
              Contact Us
            </button>
          </div>
        </div>
        
        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button 
            @click="toggleMenu" 
            :class="cn('focus:outline-none', isScrolled ? 'text-gray-700' : 'text-white')"
          >
            <Menu v-if="!isMenuOpen" :size="24" />
            <X v-else :size="24" />
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div 
      :class="cn(
        'md:hidden transition-all duration-300 overflow-hidden w-full',
        isMenuOpen ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'
      )"
    >
      <div :class="cn('px-2 pt-2 pb-3 space-y-1 sm:px-3 shadow-sm', isScrolled ? 'bg-white' : 'bg-black')">
        <RouterLink 
          to="/" 
          :class="cn('block px-3 py-2 rounded-md', isScrolled ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-200 hover:bg-gray-900')" 
          @click="closeMobileMenu"
        >
          Home
        </RouterLink>
        
        <RouterLink 
          to="/about" 
          :class="cn('block px-3 py-2 rounded-md', isScrolled ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-200 hover:bg-gray-900')" 
          @click="closeMobileMenu"
        >
          About Us
        </RouterLink>
        
        <!-- Mobile Customer Cases -->
        <div class="block">
          <button 
            @click="toggleMobileSubmenu('customerCases')"
            :class="cn('flex w-full justify-between items-center px-3 py-2 rounded-md', isScrolled ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-200 hover:bg-gray-900')"
          >
            <span>Customer Cases</span>
            <ChevronDown class="h-4 w-4" />
          </button>
          
          <div v-show="mobileSubmenus.customerCases" class="ml-4 mt-1 space-y-1">
            <RouterLink 
              to="/projects/firecat" 
              :class="cn('block px-3 py-2 rounded-md', isScrolled ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-200 hover:bg-gray-900')" 
              @click="closeMobileMenu"
            >
              FireCat 6th SENSE
            </RouterLink>
            <RouterLink 
              to="/projects/sport-retail" 
              :class="cn('block px-3 py-2 rounded-md', isScrolled ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-200 hover:bg-gray-900')" 
              @click="closeMobileMenu"
            >
              Sports Performance
            </RouterLink>
            <RouterLink 
              to="/projects/workwear" 
              :class="cn('block px-3 py-2 rounded-md', isScrolled ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-200 hover:bg-gray-900')" 
              @click="closeMobileMenu"
            >
              Workwear Climate Control
            </RouterLink>
            <RouterLink 
              to="/projects/hockey" 
              :class="cn('block px-3 py-2 rounded-md', isScrolled ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-200 hover:bg-gray-900')" 
              @click="closeMobileMenu"
            >
              Ice Hockey Elite Tracker
            </RouterLink>
            <RouterLink 
              to="/projects/pet-tracker" 
              :class="cn('block px-3 py-2 rounded-md', isScrolled ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-200 hover:bg-gray-900')" 
              @click="closeMobileMenu"
            >
              Pet Activity Counter
            </RouterLink>
          </div>
        </div>
        
        <!-- Mobile Learn More -->
        <div class="block">
          <button 
            @click="toggleMobileSubmenu('learnMore')"
            :class="cn('flex w-full justify-between items-center px-3 py-2 rounded-md', isScrolled ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-200 hover:bg-gray-900')"
          >
            <span>Learn More</span>
            <ChevronDown class="h-4 w-4" />
          </button>
          
          <div v-show="mobileSubmenus.learnMore" class="ml-4 mt-1 space-y-1">
            <RouterLink 
              to="/tech-details" 
              :class="cn('block px-3 py-2 rounded-md', isScrolled ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-200 hover:bg-gray-900')" 
              @click="closeMobileMenu"
            >
              Technology Details
            </RouterLink>
            <RouterLink 
              to="/development-process" 
              :class="cn('block px-3 py-2 rounded-md', isScrolled ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-200 hover:bg-gray-900')" 
              @click="closeMobileMenu"
            >
              Development Process
            </RouterLink>
            <button 
              @click="scrollToSection('why-wrlds')" 
              :class="cn('block w-full text-left px-3 py-2 rounded-md', isScrolled ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-200 hover:bg-gray-900')"
            >
              Why WRLDS
            </button>
          </div>
        </div>
        
        <RouterLink 
          to="/blog" 
          :class="cn('block px-3 py-2 rounded-md', isScrolled ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-200 hover:bg-gray-900')" 
          @click="closeMobileMenu"
        >
          News
        </RouterLink>
        
        <RouterLink 
          to="/careers" 
          :class="cn('block px-3 py-2 rounded-md', isScrolled ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-200 hover:bg-gray-900')" 
          @click="closeMobileMenu"
        >
          Careers
        </RouterLink>
        
        <button 
          @click="scrollToSection('contact')" 
          :class="cn('block w-full text-left px-3 py-2 rounded-md', isScrolled ? 'text-gray-700 bg-gray-200 hover:bg-gray-300' : 'text-white bg-gray-700 hover:bg-gray-600')"
        >
          Contact Us
        </button>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive } from 'vue'
import { RouterLink } from 'vue-router'
import { cn } from '@/lib/utils'
import { Menu, X, ChevronDown } from 'lucide-vue-next'

const isScrolled = ref(false)
const isMenuOpen = ref(false)
const showCustomerCases = ref(false)
const showLearnMore = ref(false)

const mobileSubmenus = reactive({
  customerCases: false,
  learnMore: false
})

const handleScroll = () => {
  if (window.scrollY > 10) {
    isScrolled.value = true
  } else {
    isScrolled.value = false
  }
}

const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
}

const closeMobileMenu = () => {
  isMenuOpen.value = false
  window.scrollTo(0, 0)
}

const toggleMobileSubmenu = (submenu: keyof typeof mobileSubmenus) => {
  mobileSubmenus[submenu] = !mobileSubmenus[submenu]
}

const scrollToSection = (id: string) => {
  const element = document.getElementById(id)
  if (element) {
    element.scrollIntoView({
      behavior: 'smooth'
    })
  }
  isMenuOpen.value = false
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>
