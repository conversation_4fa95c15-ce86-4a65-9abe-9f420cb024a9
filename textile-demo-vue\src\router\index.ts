import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/Index.vue'),
    },
    {
      path: '/projects/firecat',
      name: 'firecat',
      component: () => import('../views/FireCatProject.vue'),
    },
    {
      path: '/projects/sport-retail',
      name: 'sport-retail',
      component: () => import('../views/SportRetailProject.vue'),
    },
    {
      path: '/projects/workwear',
      name: 'workwear',
      component: () => import('../views/WorkwearProject.vue'),
    },
    {
      path: '/projects/hockey',
      name: 'hockey',
      component: () => import('../views/HockeyProject.vue'),
    },
    {
      path: '/projects/pet-tracker',
      name: 'pet-tracker',
      component: () => import('../views/PetProject.vue'),
    },
    {
      path: '/tech-details',
      name: 'tech-details',
      component: () => import('../views/TechDetails.vue'),
    },
    {
      path: '/development-process',
      name: 'development-process',
      component: () => import('../views/DevelopmentProcess.vue'),
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/About.vue'),
    },
    {
      path: '/careers',
      name: 'careers',
      component: () => import('../views/Careers.vue'),
    },
    {
      path: '/privacy-policy',
      name: 'privacy-policy',
      component: () => import('../views/PrivacyPolicy.vue'),
    },
    {
      path: '/blog',
      name: 'blog',
      component: () => import('../views/Blog.vue'),
    },
    {
      path: '/blog/:slug',
      name: 'blog-post',
      component: () => import('../views/BlogPostDetail.vue'),
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('../views/NotFound.vue'),
    },
  ],
})

export default router
