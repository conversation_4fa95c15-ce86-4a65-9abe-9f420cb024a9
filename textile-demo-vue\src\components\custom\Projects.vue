<template>
  <section id="projects" class="py-16 md:py-24 bg-gray-50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <div class="inline-block mb-2 px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
          Customer Cases
        </div>
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Real-World Applications
        </h2>
        <p class="text-gray-600 max-w-3xl mx-auto">
          Discover how our smart textile technology is transforming industries and improving lives through innovative solutions.
        </p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div 
          v-for="project in projects" 
          :key="project.id"
          class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
        >
          <div class="relative h-48 overflow-hidden">
            <img 
              :src="project.image" 
              :alt="project.title"
              class="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            />
            <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
            <div class="absolute bottom-4 left-4 right-4">
              <span class="inline-block px-2 py-1 bg-blue-600 text-white text-xs rounded-full">
                {{ project.category }}
              </span>
            </div>
          </div>
          
          <div class="p-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">
              {{ project.title }}
            </h3>
            <p class="text-gray-600 mb-4 text-sm">
              {{ project.description }}
            </p>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <component :is="project.icon" class="w-5 h-5 text-blue-600" />
                <span class="text-sm text-gray-500">{{ project.industry }}</span>
              </div>
              
              <RouterLink 
                :to="project.link"
                class="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center group"
              >
                Learn More
                <ArrowRight class="ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </RouterLink>
            </div>
          </div>
        </div>
      </div>
      
      <div class="text-center mt-12">
        <RouterLink 
          to="/projects"
          class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          View All Projects
          <ArrowRight class="ml-2 w-5 h-5" />
        </RouterLink>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
import { ArrowRight, Shield, Activity, HardHat, Zap, Heart } from 'lucide-vue-next'

const projects = [
  {
    id: 'firecat',
    title: 'FireCat 6th SENSE',
    description: 'Smart textiles for firefighter safety with real-time environmental monitoring and health tracking.',
    category: 'Safety',
    industry: 'Emergency Services',
    image: '/lovable-uploads/48ecf6e2-5a98-4a9d-af6f-ae2265cd4098.png',
    icon: Shield,
    link: '/projects/firecat'
  },
  {
    id: 'sport-retail',
    title: 'Sports Performance Tracker',
    description: 'Advanced athletic wear with integrated sensors for performance optimization and injury prevention.',
    category: 'Sports',
    industry: 'Athletics',
    image: '/lovable-uploads/48e540e5-6a25-44e4-b3f7-80f3bfc2777a.png',
    icon: Activity,
    link: '/projects/sport-retail'
  },
  {
    id: 'workwear',
    title: 'Workwear Climate Control',
    description: 'Industrial clothing with temperature regulation and environmental hazard detection.',
    category: 'Industrial',
    industry: 'Manufacturing',
    image: '/lovable-uploads/c8f4b7a1-2d3e-4f5a-8b9c-1e2f3a4b5c6d.png',
    icon: HardHat,
    link: '/projects/workwear'
  },
  {
    id: 'hockey',
    title: 'Ice Hockey Elite Tracker',
    description: 'Professional hockey equipment with performance analytics and impact monitoring.',
    category: 'Sports',
    industry: 'Professional Sports',
    image: '/lovable-uploads/d9e5c8b2-3f4a-5e6b-9c8d-2f3e4a5b6c7d.png',
    icon: Zap,
    link: '/projects/hockey'
  },
  {
    id: 'pet-tracker',
    title: 'Pet Activity Counter',
    description: 'Smart collars for comprehensive pet health and activity monitoring.',
    category: 'Consumer',
    industry: 'Pet Care',
    image: '/lovable-uploads/e0f6d9c3-4a5b-6f7c-0d9e-3f4a5b6c7d8e.png',
    icon: Heart,
    link: '/projects/pet-tracker'
  }
]
</script>
