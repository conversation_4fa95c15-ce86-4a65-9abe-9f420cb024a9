<template>
  <section id="features" class="relative bg-white overflow-hidden py-10 md:py-[50px] w-full">
    <div class="w-full px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-10 max-w-3xl mx-auto">
        <div class="inline-block mb-2 px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm font-medium">
          Textile Sensor Applications
        </div>
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Smart Solutions Across Industries
        </h2>
        <p class="text-gray-600 mt-4">
          Our textile sensor technology transforms ordinary fabrics into intelligent interfaces that collect data, monitor conditions, and enhance performance across diverse sectors.
        </p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div 
          v-for="(feature, index) in features" 
          :key="index"
          class="feature-item rounded-xl overflow-hidden transform transition-all duration-500 relative shadow-lg hover:-translate-y-1 h-[280px]"
          @mouseenter="hoveredFeature = index"
          @mouseleave="hoveredFeature = null"
        >
          <div class="absolute inset-0 w-full h-full">
            <img 
              :src="feature.image" 
              :alt="feature.title" 
              class="w-full h-full object-cover transition-all duration-300 grayscale"
            />
            <div 
              :class="cn(
                'absolute inset-0 transition-opacity duration-300',
                hoveredFeature === index ? 'bg-black/50' : 'bg-black/70'
              )"
            ></div>
          </div>
          
          <div class="relative z-10 flex flex-col justify-between p-6 h-full">
            <div>
              <div 
                :class="cn(
                  'inline-block p-3 bg-gray-800/40 backdrop-blur-sm rounded-lg transition-all duration-300 transform mb-4',
                  hoveredFeature === index ? 'hover:scale-110' : ''
                )"
              >
                <div 
                  :class="cn(
                    'transform transition-transform duration-300',
                    hoveredFeature === index ? 'rotate-12' : ''
                  )"
                >
                  <component :is="feature.icon" class="w-10 h-10 text-white transition-transform duration-300 transform" />
                </div>
              </div>
              <h3 class="text-xl font-semibold text-white mb-2">
                {{ feature.title }}
              </h3>
              <p class="text-white/90 text-sm">
                {{ feature.description }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Activity, Shield, HardHat, Zap, Factory, Heart } from 'lucide-vue-next'
import { cn } from '@/lib/utils'

const hoveredFeature = ref<number | null>(null)

const features = [
  {
    icon: Activity,
    title: "Sports Performance",
    description: "Specialized fabrics that analyze form, provide instant feedback, and help prevent injuries in athletic equipment.",
    image: "/lovable-uploads/48e540e5-6a25-44e4-b3f7-80f3bfc2777a.png"
  },
  {
    icon: Shield,
    title: "Military & Defense",
    description: "Tactical gear with embedded sensors for soldier health monitoring, environmental awareness, and enhanced safety.",
    image: "/lovable-uploads/48ecf6e2-5a98-4a9d-af6f-ae2265cd4098.png"
  },
  {
    icon: HardHat,
    title: "Industrial Safety",
    description: "Workwear that monitors environmental conditions, detects hazards, and ensures worker safety in demanding environments.",
    image: "/lovable-uploads/c8f4b7a1-2d3e-4f5a-8b9c-1e2f3a4b5c6d.png"
  },
  {
    icon: Heart,
    title: "Healthcare",
    description: "Medical textiles that continuously monitor patient vitals and provide real-time health data to healthcare providers.",
    image: "/lovable-uploads/d9e5c8b2-3f4a-5e6b-9c8d-2f3e4a5b6c7d.png"
  },
  {
    icon: Zap,
    title: "Smart Clothing",
    description: "Everyday garments enhanced with sensors for fitness tracking, posture monitoring, and lifestyle optimization.",
    image: "/lovable-uploads/e0f6d9c3-4a5b-6f7c-0d9e-3f4a5b6c7d8e.png"
  },
  {
    icon: Factory,
    title: "Manufacturing",
    description: "Industrial textiles that monitor production processes, quality control, and equipment performance in real-time.",
    image: "/lovable-uploads/f1a7e0d4-5b6c-7a8d-1e0f-4a5b6c7d8e9f.png"
  }
]
</script>
