
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 15%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 15%;

    --primary: 0 0% 10%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 95%;
    --secondary-foreground: 0 0% 15%;

    --muted: 0 0% 95%;
    --muted-foreground: 0 0% 40%;

    --accent: 0 0% 95%;
    --accent-foreground: 0 0% 15%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 0 0% 50%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html, body {
    @apply m-0 p-0 overflow-x-hidden;
    width: 100%;
    max-width: 100%;
  }

  body {
    @apply bg-white text-foreground font-space;
  }
}

.section-container {
  @apply w-full px-4 sm:px-6 lg:px-8 py-12;
}

.hover-lift {
  @apply transition-transform duration-300;
}

.hover-lift:hover {
  @apply transform -translate-y-1;
}

.smooth-scroll {
  scroll-behavior: smooth;
}

/* Banner styles */
.banner-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  text-align: center;
  z-index: 10;
}

.banner-title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

@media (min-width: 768px) {
  .banner-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 1.5rem;
  }
}

.banner-subtitle {
  font-size: clamp(1rem, 2.5vw, 1.2rem);
  max-width: 90%;
  line-height: 1.5;
}

@media (min-width: 768px) {
  .banner-subtitle {
    max-width: 800px;
  }
}

/* Animation classes */
@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes rotate-float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(10deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

@keyframes pulse-line {
  0% {
    opacity: 0.3;
    stroke-width: 1px;
  }
  50% {
    opacity: 1;
    stroke-width: 2px;
  }
  100% {
    opacity: 0.3;
    stroke-width: 1px;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animation-delay-100 {
  animation-delay: 100ms !important;
}

.animation-delay-200 {
  animation-delay: 200ms !important;
}

.animation-delay-300 {
  animation-delay: 300ms !important;
}

.animation-delay-400 {
  animation-delay: 400ms !important;
}

.animation-delay-500 {
  animation-delay: 500ms !important;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-circle {
  animation: float 6s ease-in-out infinite;
}

.animate-line {
  animation: pulse-line 4s ease-in-out infinite;
}

.animate-rotate {
  animation: rotate 20s linear infinite;
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in {
  animation: slideIn 0.6s ease-out forwards;
}

/* Hover animations */
.hover-underline {
  position: relative;
}

.hover-underline:after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: currentColor;
  transition: width 0.3s ease;
}

.hover-underline:hover:after {
  width: 100%;
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* New pulse animation */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}
