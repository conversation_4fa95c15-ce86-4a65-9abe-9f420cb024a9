<template>
  <button
    @click="scrollToContact"
    class="fixed bottom-6 right-6 bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-full shadow-lg transition-all duration-300 hover:scale-110 z-40"
  >
    <MessageSquare class="w-6 h-6" />
  </button>
</template>

<script setup lang="ts">
import { MessageSquare } from 'lucide-vue-next'

const scrollToContact = () => {
  const contactSection = document.getElementById('contact')
  if (contactSection) {
    contactSection.scrollIntoView({
      behavior: 'smooth'
    })
  }
}
</script>
