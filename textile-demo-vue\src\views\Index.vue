<template>
  <PageLayout>
    <Hero />
    <Features />
    <WhyWrlds />
    <Projects />
    <BlogPreview />
  </PageLayout>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useHead } from '@vueuse/head'
import PageLayout from '@/components/custom/PageLayout.vue'
import Hero from '@/components/custom/Hero.vue'
import Features from '@/components/custom/Features.vue'
import WhyWrlds from '@/components/custom/WhyWrlds.vue'
import Projects from '@/components/custom/Projects.vue'
import BlogPreview from '@/components/custom/BlogPreview.vue'

// SEO Meta tags
useHead({
  title: 'WRLDS - Smart Textile Technology',
  meta: [
    {
      name: 'description',
      content: 'WRLDS Technologies: Pioneering smart engineering solutions with textile sensors for sports, safety, and performance monitoring.'
    },
    {
      name: 'keywords',
      content: 'smart textiles, wearable technology, textile sensors, sports tech, safety monitoring, performance analytics, manufacturing'
    },
    {
      name: 'author',
      content: 'WRLDS Technologies'
    },
    // Open Graph
    {
      property: 'og:title',
      content: 'WRLDS - Smart Textile Technology'
    },
    {
      property: 'og:description',
      content: 'WRLDS Technologies: Pioneering smart engineering solutions across industries with intelligent textile sensors.'
    },
    {
      property: 'og:image',
      content: 'https://wrlds.com/lovable-uploads/526dc38a-25fa-40d4-b520-425b23ae0464.png'
    },
    {
      property: 'og:image:width',
      content: '1200'
    },
    {
      property: 'og:image:height',
      content: '630'
    },
    {
      property: 'og:url',
      content: 'https://wrlds.com'
    },
    {
      property: 'og:type',
      content: 'website'
    },
    {
      property: 'og:site_name',
      content: 'WRLDS Technologies'
    },
    {
      property: 'og:locale',
      content: 'en_US'
    },
    // Twitter
    {
      name: 'twitter:card',
      content: 'summary_large_image'
    },
    {
      name: 'twitter:title',
      content: 'WRLDS - Smart Textile Technology'
    },
    {
      name: 'twitter:description',
      content: 'Pioneering smart engineering solutions with textile sensors.'
    },
    {
      name: 'twitter:image',
      content: 'https://wrlds.com/lovable-uploads/526dc38a-25fa-40d4-b520-425b23ae0464.png'
    },
    {
      name: 'twitter:site',
      content: '@wrldstechnologies'
    }
  ]
})

// Fix any ID conflicts when the page loads
onMounted(() => {
  const contactElements = document.querySelectorAll('[id="contact"]')
  if (contactElements.length > 1) {
    // If there are multiple elements with id="contact", rename one
    contactElements[1].id = 'contact-footer'
  }
})
</script>
